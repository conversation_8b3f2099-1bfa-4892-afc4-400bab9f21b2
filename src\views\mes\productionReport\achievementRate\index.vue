<template>
  <div class="main-box">
    <ProChart
      :options="chartOptions"
      :fetch-api="fetchData"
      :tool-buttons="['refresh', 'download', 'table', 'compare']"
      :search-fields="searchFields"
      @data-loaded="handleDataLoaded"
      :compare-list="compareList"
      default-compare-id="达成率"
      :machines="machines"
      :init-params="initParams"
      id="651535198396485"
    >
      <template #toolbar-left>
        <el-button type="primary" @click="openPlanStandardDialog"> 修改计划标准 </el-button>
      </template>
      <!-- 修改计划标准弹窗 -->
    </ProChart>
    <el-dialog v-model="planStandardDialogVisible" title="修改计划标准" width="500px" :before-close="handleDialogClose">
      <el-form ref="planStandardFormRef" :model="planStandardForm" :rules="planStandardRules" label-width="120px">
        <el-form-item label="PPM" prop="ppm">
          <el-input-number v-model="planStandardForm.ppm" :min="0" :precision="0" placeholder="请输入PPM值" style="width: 100%" />
        </el-form-item>
        <el-form-item label="每天运行时长" prop="dailyRunningHours">
          <el-input-number
            v-model="planStandardForm.dailyRunningHours"
            :min="0"
            :max="24"
            :precision="1"
            placeholder="请输入每天运行时长"
            style="width: 100%"
          />
          <span style="margin-left: 8px; color: #909399">小时</span>
        </el-form-item>
        <el-form-item>
          <div style="font-size: 12px; color: #909399">
            说明：每小时计划标准 = PPM × 60
            <br />
            如输入12 PPM，则每小时计划标准为 {{ planStandardForm.ppm * 60 }}
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="planStandardDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePlanStandard" :loading="saving"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx" name="计划达成率">
import { ref, onMounted } from "vue";
import type { ColumnProps } from "@/components/ProTable/interface";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import moment from "moment";
import { productionReportCapacityApi } from "@/api/modules/mes/productionReport";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

// 计划标准弹窗相关
const planStandardDialogVisible = ref(false);
const planStandardFormRef = ref<FormInstance>();
const saving = ref(false);
const planStandardForm = ref({
  ppm: 0,
  dailyRunningHours: 8
});

// 用户自定义的计划标准值（每小时）
const customHourlyStandard = ref<number | null>(null);

const planStandardRules: FormRules = {
  ppm: [
    { required: true, message: "请输入PPM值", trigger: "blur" },
    { type: "number", min: 0, message: "PPM值不能小于0", trigger: "blur" }
  ],
  dailyRunningHours: [
    { required: true, message: "请输入每天运行时长", trigger: "blur" },
    { type: "number", min: 0, max: 24, message: "运行时长应在0-24小时之间", trigger: "blur" }
  ]
};

// 动态获取选项
const machineList = ref<any[]>([]); // 新增机台列表响应式变量
const initParams = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")],
  type: 3
});
const machines = ref<any[]>([]);
const root = document.documentElement;
const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");
// 颜色配置常量
const COLORS = {
  实际产出: "#00bfff",
  计划标准: "#ff4d4f",
  达成率: "#fac858",
  font: chartColor,
  splitLine: "#eee"
};

const compareList = [
  {
    label: t("common.compareList.实际产出"),
    value: "实际产出"
  },
  {
    label: t("common.compareList.计划标准"),
    value: "计划标准"
  },
  {
    label: t("common.compareList.达成率"),
    value: "达成率"
  }
];

// 搜索字段配置
const searchFields = ref<ColumnProps[]>([
  {
    prop: "time",
    label: t("common.compareList.time"),
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "To",
        "start-placeholder": "Start date",
        "end-placeholder": "End date"
      }
    },
    isShow: false
  }
]);

// 图表配置
const chartOptions = ref({
  title: {
    // text: "计划达成率统计看板",
    subtext: t("common.compareList.averageAchievementRate") + ": 0", //"平均达成率：0",
    left: "center",
    top: -12,
    textStyle: {
      fontSize: 16,
      color: COLORS.font
    },
    subtextStyle: {
      color: COLORS.font
    }
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: t("productionReport.capacity.switchToLine"), // 切换为折线图
          bar: t("productionReport.capacity.switchToBar") // 切换为柱状图
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    formatter: (params: any) => {
      const 实际产出 = params.find((p: any) => p.seriesName === t("common.compareList.实际产出"));
      const 计划标准 = params.find((p: any) => p.seriesName === t("common.compareList.计划标准"));
      const 达成率 = params.find((p: any) => p.seriesName === t("common.compareList.达成率"));
      return `
        <div style="padding:5px;min-width:120px">
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.实际产出};border-radius:50%"></span>
            ${t("common.compareList.实际产出")}: ${实际产出?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.计划标准};border-radius:50%"></span>
            ${t("common.compareList.计划标准")}: ${计划标准?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.达成率};border-radius:50%"></span>
            ${t("common.compareList.达成率")}: ${达成率?.data}%
          </div>
        </div>
      `;
    }
  },
  legend: {
    data: [t("common.compareList.实际产出"), t("common.compareList.计划标准"), t("common.compareList.达成率")],
    top: 17,
    textStyle: { color: COLORS.font }
  },
  xAxis: {
    type: "category",
    data: [],
    name: t("common.compareList.time"),
    axisLabel: {
      color: COLORS.font,
      interval: 0, // 强制显示所有标签
      rotate: 45 // 旋转标签以避免重叠
    }
  },
  yAxis: [
    {
      type: "value",
      name: t("common.compareList.quantityOrRatio"), //数量/比率 (%)
      min: 0,
      max: 100,
      axisLabel: {
        color: COLORS.font,
        formatter: (value: number) => `${value}`
      },
      splitLine: { lineStyle: { color: COLORS.splitLine } }
    }
  ],
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    // top: 100,
    containLabel: true
  },
  series: []
});
const fetchMachines = async () => {
  try {
    console.log("开始获取机台列表");
    const response = await alramAnalysisApi.getListMesReportData({ Type: 0 });
    const list = response.data.list || [];

    machineList.value = list.map(item => ({
      id: item.MachineName || "未知机台",
      name: item.MachineName || "未知机台"
    }));
    // // 添加：设置默认选中第一个机台
    // if (machineList.value.length > 0) {
    //   searchParam.value.machine = machineList.value[0].id; // 设置默认机台 id
    // }

    console.log("机台列表已更新:", machineList.value);
  } catch (error) {
    console.error("机台列表请求失败:", error);
    ElMessage.error("获取机台列表失败");
  }
};
function transformData(responseData, timeType) {
  const machineMap = new Map();
  const timeSet = new Set();
  const machineIdSet = new Set();

  let formatPattern;
  switch (timeType) {
    case "Hour":
      formatPattern = "HH:00";
      break;
    case "Mon":
      formatPattern = "MM-DD";
      break;
    case "Year":
      formatPattern = "YYYY-MM";
      break;
    default:
      formatPattern = "HH:00";
  }

  // 遍历响应数据，按机台分组并收集所有时间点和机台 ID
  responseData.forEach(item => {
    const machine = item.machine;
    let startTime;
    // if (timeType === "Hour") {
    //   const start = moment(item.starttime).format("HH:00");
    //   const end = moment(item.starttime).add(1, "hour").format("HH:00");
    //   startTime = `${start}-${end}`;
    // } else {
    //   startTime = moment(item.starttime).format(formatPattern);
    // }
    startTime = moment(item.start_time).format(formatPattern);
    const 实际产出 = item.actualquantity;
    const 计划标准 = item.plannedquantity;
    const 达成率 = item.achievementrate;

    if (!machineMap.has(machine)) {
      machineMap.set(machine, {
        machine,
        实际产出: [],
        计划标准: [],
        达成率: [],
        total实际产出: 0,
        total计划标准: 0,
        total达成率: 0
      });
    }

    const machineData = machineMap.get(machine);
    machineData.实际产出.push(实际产出);
    machineData.计划标准.push(计划标准);
    machineData.达成率.push(达成率);
    machineData.total实际产出 += 实际产出;
    machineData.total计划标准 += 计划标准;
    machineData.total达成率 += 达成率;

    timeSet.add(startTime);
    machineIdSet.add(item.machine);
  });

  // 对时间点进行排序
  const categories = Array.from(timeSet).sort();

  // 计算总平均值
  machineMap.forEach(machineData => {
    machineData.total实际产出 = (machineData.total实际产出 / machineData.实际产出.length).toFixed(1);
    machineData.total计划标准 = (machineData.total计划标准 / machineData.计划标准.length).toFixed(1);
    machineData.total达成率 = (machineData.total达成率 / machineData.达成率.length).toFixed(1);
  });

  const allmachine = Array.from(machineMap.values());

  const compare = [
    {
      实际产出: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.实际产出,
        total: parseFloat(machine.total实际产出)
      })),
      计划标准: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.计划标准,
        total: parseFloat(machine.total计划标准)
      })),
      达成率: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.达成率,
        total: parseFloat(machine.total达成率)
      }))
    }
  ];

  // 构建 machines 数组
  const machines = Array.from(machineIdSet).map(deck => ({
    id: deck,
    name: deck
  }));

  return {
    allmachine,
    compare,
    categories,
    machines
  };
}

// 修改数据获取函数
const fetchData = async (params: any) => {
  const time = {
    StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss").toString(),
    EndDate: moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString()
  };
  const query = {
    ...time,
    type: params.type
  };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);
  let mode = "Hour"; // 默认值
  if (data && data.list.length > 0) {
    mode = data.list[0].type;
  }
  const data1 = transformData(data.list, mode);
  machines.value = data1.machines;

  // 普通模式数据请求
  if (!params.compareMode) {
    const machine = params.machine;
    const machineInfo = data1.allmachine.find(item => item.machine === machine) || data1.allmachine[0];
    if (!machineInfo) {
      console.error(`未找到机台 ${machine} 的数据`);
      return {
        data: {
          categories: ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"],
          seriesData: [[], [], []],
          isCompare: false
        }
      };
    }
    const { 实际产出, 计划标准, 达成率 } = machineInfo;
    return {
      data: {
        categories: data1.categories,
        seriesData: [实际产出, 计划标准, 达成率],
        isCompare: false
      }
    };
  }

  // 对比模式数据请求
  return {
    data: {
      isCompare: true,
      categories: data1.categories,
      compare: data1.compare
    }
  };
};

// 修改数据加载回调
const handleDataLoaded = (data: any) => {
  // 普通模式数据处理
  if (!data.isCompare) {
    const [实际产出, 原始计划标准, 达成率] = data.seriesData;
    console.log("达成率数据:", 达成率); // 添加调试信息

    // 如果用户设置了自定义计划标准，使用自定义值；否则使用原始数据
    const 计划标准 = customHourlyStandard.value !== null ? new Array(data.categories.length).fill(customHourlyStandard.value) : 原始计划标准;

    chartOptions.value = {
      ...chartOptions.value,
      title: {
        ...chartOptions.value.title,
        subtext: `${t("common.compareList.averageAchievementRate")}：${(达成率.reduce((a: number, b: number) => a + b, 0) / 达成率.length).toFixed(1)}`
      },
      xAxis: {
        ...chartOptions.value.xAxis,
        data: data.categories
      },
      label: {
        show: true,
        position: "top",
        formatter: "{c}"
      },
      series: [
        {
          name: t("common.compareList.实际产出"),
          type: "bar",
          data: 实际产出,
          itemStyle: { color: COLORS.实际产出 }
        },
        {
          name: t("common.compareList.计划标准"),
          type: "line",
          data: 计划标准,
          itemStyle: { color: COLORS.计划标准 },
          lineStyle: { color: COLORS.计划标准, width: 2 },
          symbol: "circle",
          symbolSize: 6
        },
        {
          name: t("common.compareList.达成率"),
          type: "bar",
          data: 达成率,
          itemStyle: { color: COLORS.达成率 }
        }
      ]
    };
  }
};

// 计划标准弹窗相关方法
const openPlanStandardDialog = () => {
  planStandardDialogVisible.value = true;
  // 可以在这里加载当前的计划标准配置
  loadCurrentPlanStandard();
};

const handleDialogClose = () => {
  planStandardFormRef.value?.resetFields();
  planStandardDialogVisible.value = false;
};

const loadCurrentPlanStandard = async () => {
  try {
    // 这里可以调用API获取当前的计划标准配置
    // const { data } = await productionReportCapacityApi.getPlanStandard();
    // planStandardForm.value = data;
  } catch (error) {
    console.error("加载计划标准配置失败:", error);
  }
};

// 更新图表中的计划标准数据
const updateChartWithNewStandard = (hourlyStandard: number) => {
  // 获取当前图表的series数据
  const currentSeries = chartOptions.value.series;
  if (currentSeries && currentSeries.length > 0) {
    // 找到计划标准的series（通常是第二个，索引为1）
    const planStandardSeriesIndex = currentSeries.findIndex((series: any) => series.name === t("common.compareList.计划标准"));

    if (planStandardSeriesIndex !== -1) {
      // 获取当前的时间点数量
      const categoriesLength = chartOptions.value.xAxis?.data?.length || 0;

      // 创建新的计划标准数据数组，所有时间点都使用相同的hourlyStandard值
      const newPlanStandardData = new Array(categoriesLength).fill(hourlyStandard);

      // 更新计划标准series的数据
      currentSeries[planStandardSeriesIndex].data = newPlanStandardData;

      // 触发图表更新
      chartOptions.value = { ...chartOptions.value };

      console.log(`计划标准已更新为每小时 ${hourlyStandard} 件`);
    }
  } else {
    // 如果当前没有series数据，说明图表还没有加载数据
    // 我们可以设置一个标记，等数据加载后再应用新的计划标准
    console.log("图表数据尚未加载，计划标准将在数据加载后应用");
  }
};

const savePlanStandard = async () => {
  if (!planStandardFormRef.value) return;

  try {
    await planStandardFormRef.value.validate();
    saving.value = true;

    const params = {
      ...planStandardForm.value,
      date: moment().format("YYYY-MM-DD")
    };

    // 计算新的计划标准值（每小时 = PPM × 60）
    const hourlyStandard = planStandardForm.value.ppm * 60;

    // 保存用户自定义的计划标准值
    customHourlyStandard.value = hourlyStandard;

    // 先更新本地图表数据，不管API是否成功
    updateChartWithNewStandard(hourlyStandard);

    // 尝试保存到后端
    try {
      await productionReportCapacityApi.savePlanStandard(params);
      ElMessage.success("计划标准保存成功");
    } catch (apiError) {
      console.error("保存计划标准API失败:", apiError);
      ElMessage.warning("数据已更新到图表，但保存到服务器失败");
    }

    planStandardDialogVisible.value = false;
  } catch (error) {
    console.error("保存计划标准失败:", error);
    ElMessage.error("保存失败，请稍后重试");
  } finally {
    saving.value = false;
  }
};

// 添加 onMounted 钩子，页面加载时自动触发搜索
onMounted(async () => {
  await fetchMachines(); // 挂载时获取机台
});
</script>
